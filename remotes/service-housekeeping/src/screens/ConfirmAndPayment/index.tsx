import React, { useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  BookingButton,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  DatePicker,
  DateTimeHelpers,
  FontSizes,
  IDate,
  LocationPostTask,
  PAYMENT_METHOD,
  PaymentDetail,
  PaymentMethod,
  Spacing,
  TimePicker,
  TouchableOpacity,
  useI18n,
  useSettingsStore,
} from '@btaskee/design-system';

import { TaskDetail } from '@components';
// import { TaskDetail, VATInvoice } from '@components';
import { useAppNavigation, usePostTask } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ConfirmAndPayment = () => {
  const { t } = useI18n('common');

  const { getPrice, postTask } = usePostTask();
  const {
    setAddress,
    service,
    date,
    setDateTime,
    address,
    homeNumber,
    paymentMethod,
    promotion,
    price,
    note,
    duration,
    schedule,
    isEnableSchedule,
    setPromotion,
    setPaymentMethod,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const navigation = useAppNavigation();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date as IDate);
  const modalRef = useRef<CModalHandle | null>(null);

  const removeTetInService = () => {
    // Clear isTet in service
    delete service?.isTet;
    setIsTet(false);
  };

  const _changeToRegularBooking = () => {
    // Require change date if the choosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'day')
      .endOf('date');
    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone,
      firstDate: date as IDate,
      secondDate: maxDate,
    });
    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hour');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      removeTetInService();
    }
  };

  // update date time when user change
  const onChangeDateTime = (dateObj: IDate) => {
    // check spam, call api with same data
    const isSame = DateTimeHelpers.checkIsSame({
      timezone,
      firstDate: date as IDate,
      secondDate: dateObj,
      unit: 'minute',
    });
    if (isSame) return null;

    setSelectedDate(dateObj);
  };

  const _changeNewDate = async () => {
    setDateTime(selectedDate);
    // recaculate duration and estimated time, only Home Cooking service
    getPrice();
    removeTetInService();
  };

  return (
    <BlockView style={styles.container}>
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        testID="scrollViewStep4"
        contentContainerStyle={styles.content}
      >
        <LocationPostTask
          address={address}
          homeNumber={homeNumber}
          setAddress={setAddress}
        />

        <TaskDetail
          note={note}
          date={date}
          duration={duration}
          schedule={schedule}
          isEnableSchedule={isEnableSchedule}
          timezone={timezone}
        />

        <PaymentDetail price={price} />

        <PaymentMethod
          paymentMethod={paymentMethod}
          promotion={promotion}
          setPromotion={setPromotion}
          setPaymentMethod={setPaymentMethod}
          blackList={[PAYMENT_METHOD.cash]}
        />

        <ConditionView
          condition={Boolean(isTet)}
          viewTrue={
            <TouchableOpacity
              style={styles.btn}
              onPress={() => _changeToRegularBooking()}
            >
              <CText
                center
                bold
                style={{
                  color: Colors.SECONDARY_COLOR,
                  fontSize: FontSizes.SIZE_16,
                }}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK')}
              </CText>
              <CText
                center
                size={FontSizes.SIZE_16}
                margin={{ top: Spacing.SPACE_04 }}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
              </CText>
            </TouchableOpacity>
          }
        />
        <ConditionView
          condition={Boolean(isTet)}
          viewTrue={
            <CModal
              hideButtonClose
              ref={modalRef}
              title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
              actions={[
                {
                  text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
                  onPress: _changeNewDate,
                  disabled: DateTimeHelpers.checkIsSame({
                    timezone,
                    firstDate: date,
                    secondDate: selectedDate,
                  }),
                },
              ]}
            >
              <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
              <BlockView>
                <DatePicker
                  title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
                  value={selectedDate}
                  onChange={onChangeDateTime}
                  settingSystem={settings?.settingSystem}
                  timezone={timezone}
                />

                <TimePicker
                  noShowTitle={false}
                  title={t('STEP_4_UPDATE_TIME_TITLE')}
                  value={selectedDate}
                  onChange={onChangeDateTime}
                  settingSystem={settings?.settingSystem}
                  timezone={timezone}
                />
              </BlockView>
            </CModal>
          }
        />
      </ScrollView>

      <BookingButton
        price={price}
        navigation={navigation}
        onPostTask={postTask}
      />
    </BlockView>
  );
};
