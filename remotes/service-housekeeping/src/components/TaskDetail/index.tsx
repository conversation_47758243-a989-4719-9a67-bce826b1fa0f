import React from 'react';
import {
  BlockView,
  Card,
  ConditionView,
  CText,
  IDate,
  ITaskDetail,
  ITimezone,
  WorkingTime,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface ITaskDetailProps {
  isEnableSchedule?: boolean;
  duration?: number;
  schedule?: string[];
  note?: string;
  date?: IDate;
  hostelName?: string;
  dataTask?: ITaskDetail;
  timezone: ITimezone;
}

export const TaskDetail = ({
  isEnableSchedule,
  duration,
  schedule,
  note,
  date,
  timezone,
}: ITaskDetailProps) => {
  const { t } = useI18n();

  return (
    <BlockView>
      <BlockView
        row
        style={styles.panel}
      >
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card style={styles.cardTaskDetailContainer}>
        <WorkingTime
          date={date}
          schedule={schedule}
          duration={duration}
          isEnableSchedule={Boolean(isEnableSchedule)}
          timezone={timezone}
        />
        <Detail />
        <ConditionView
          condition={Boolean(note)}
          viewTrue={
            <TaskDetailItem
              title={t('LABEL_NOTE_FOR_TASKER')}
              value={note}
            />
          }
        />
      </Card>
    </BlockView>
  );
};
