import {
  AppStorage,
  createZustand,
  IAddons,
  IAddress,
  IDate,
  IHostelType,
  IHousekeepingLocation,
  IHouseKeepingOption,
  IListRoomImage,
  IOptionAreaOfficeCleaning,
  IPrice,
  IRoomType,
  IService,
  IUser,
  IVatInfo,
  Maybe,
  Requirement,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AppState {
  address: IHousekeepingLocation;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: Maybe<IDate>;
  schedule: string[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: Maybe<IPrice>;
  service: Maybe<IService>;
  paymentMethod: any;
  promotion: any;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  weekdays: number[];
  isFirstOpen: boolean;
  detailOfficeCleaning: Maybe<IOptionAreaOfficeCleaning>;
  vatInfo: Maybe<IVatInfo>;
  isUpdateVatInfoToUser: boolean;
  forceTasker: Maybe<IUser>;
  dateOptions: Maybe<IDate[]>;
  isEnableSchedule: boolean;

  // housekeeping state
  hostelPlace: Maybe<IHousekeepingLocation>;
  homeType: Maybe<IHostelType>;
  rooms: IRoomType[];
  options: IHouseKeepingOption[];
  roomNumber: string;
  listRoomsImages: IListRoomImage[];

  setAddress: (address: IHousekeepingLocation) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date?: IDate) => void;
  setSchedule: (schedule?: string[]) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price?: Maybe<IPrice>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: IService) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setWeekdays: (weekdays?: number[]) => void;
  setIsFirstOpen: () => void;
  setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) => void;
  setVatInfo: (vatInfo: IVatInfo) => void;
  setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) => void;
  setForceTasker: (forceTasker: Maybe<IUser>) => void;
  setDateOptions: (dateOptions: IDate[]) => void;
  setDate: (date: IDate, service?: Maybe<IService>) => void;
  setEnabledSchedule: (isEnableSchedule: boolean) => void;

  // housekeeping state
  setHostelPlace: (hostelPlace: Maybe<IHousekeepingLocation>) => void;
  setHomeType: (homeType: Maybe<IHostelType>) => void;
  setRooms: (rooms: IRoomType[]) => void;
  setOptions: (options: IHouseKeepingOption[]) => void;
  setRoomNumber: (roomNumber: string) => void;
  setListRoomsImages: (listRoomsImages: IListRoomImage[]) => void;
  updateRoomImages: (roomImageData: IListRoomImage) => void;
  resetHousekeepingState: () => void;
  resetState: () => void;
}

export const usePostTaskStore = createZustand<AppState>()(
  persist(
    (set, get) => ({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      schedule: [],
      isEnabledSchedule: false,
      note: '',
      isApplyNoteForAllTask: false,
      homeNumber: '',
      price: null,
      service: null,
      paymentMethod: null,
      promotion: null,
      isLoadingPrice: false,
      loadingPostTask: false,
      relatedTask: null,
      weekdays: [],
      isFirstOpen: true,
      detailOfficeCleaning: null,
      vatInfo: null,
      isUpdateVatInfoToUser: false,
      forceTasker: null,
      dateOptions: null,
      isEnableSchedule: false,

      // housekeeping state
      hostelPlace: null,
      homeType: null,
      rooms: [],
      options: [],
      roomNumber: '',
      listRoomsImages: [],

      setLoadingPostTask: (loadingPostTask: boolean) =>
        set({ loadingPostTask: loadingPostTask }),
      setAddress: (address: IHousekeepingLocation) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setRequirements: (requirements: Requirement[]) =>
        set({ requirements: requirements }),
      setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
      setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
        set({ isAutoChooseTasker: isAutoChooseTasker }),
      setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
        set({ isFavouriteTasker: isFavouriteTasker }),
      setGender: (gender: string) => set({ gender: gender }),
      setAddons: (addons: IAddons[]) => set({ addons: addons }),
      setPet: (pet: any) => set({ pet: pet }),
      setDateTime: (date?: IDate) => set({ date: date }),
      setSchedule: (schedule?: string[]) => set({ schedule: schedule }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price?: Maybe<IPrice>) => set({ price: price }),
      setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
      setPaymentMethod: (paymentMethod: any) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion: any) => set({ promotion: promotion }),
      setLoadingPrice: (isLoadingPrice: boolean) =>
        set({ isLoadingPrice: isLoadingPrice }),
      setService: (service: IService) => set({ service: service }),
      setWeekdays: (weekdays?: number[]) => set({ weekdays: weekdays }),
      setIsFirstOpen: () => set({ isFirstOpen: false }),
      setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) =>
        set({ detailOfficeCleaning: detail }),
      setVatInfo: (vatInfo: IVatInfo) => set({ vatInfo: vatInfo }),
      setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) =>
        set({ isUpdateVatInfoToUser: isUpdateVatInfoToUser }),
      setForceTasker: (forceTasker: Maybe<IUser>) =>
        set({ forceTasker: forceTasker }),
      setDateOptions: (dateOptions: IDate[]) =>
        set({ dateOptions: dateOptions }),
      setDate: (date: IDate) => set({ date: date }),
      setEnabledSchedule: (isEnableSchedule: boolean) =>
        set({ isEnableSchedule: isEnableSchedule }),

      setHostelPlace: (hostelPlace: Maybe<IHousekeepingLocation>) =>
        set({ hostelPlace: hostelPlace }),
      setHomeType: (homeType: Maybe<IHostelType>) =>
        set({ homeType: homeType }),
      setRooms: (rooms: IRoomType[]) => set({ rooms: rooms }),
      setOptions: (options: IHouseKeepingOption[]) => set({ options: options }),
      setRoomNumber: (roomNumber: string) => set({ roomNumber: roomNumber }),
      setListRoomsImages: (listRoomsImages: IListRoomImage[]) =>
        set({ listRoomsImages: listRoomsImages }),
      updateRoomImages: (roomImageData: IListRoomImage) =>
        set((state) => {
          const listRoomsImages = [...(state.listRoomsImages || [])];
          const existingIndex = listRoomsImages.findIndex(
            (room) => room.name === roomImageData.name,
          );

          if (existingIndex === -1) {
            // Add new room images
            listRoomsImages.push(roomImageData);
          } else {
            // Update existing room images
            listRoomsImages[existingIndex] = roomImageData;
          }

          return { listRoomsImages };
        }),
      resetHousekeepingState: () =>
        set({
          price: null,
          homeType: null,
          rooms: [],
          options: [],
          roomNumber: '',
          listRoomsImages: [],
        }),
      resetState: () =>
        set({
          address: {},
          duration: 0,
          requirements: [],
          isPremium: false,
          isAutoChooseTasker: true,
          isFavouriteTasker: false,
          gender: '',
          pet: '',
          addons: [],
          date: null,
          schedule: [],
          isEnabledSchedule: false,
          note: '',
          isApplyNoteForAllTask: false,
          homeNumber: '',
          price: null,
          service: null,
          paymentMethod: null,
          promotion: null,
          isLoadingPrice: false,
          loadingPostTask: false,
          weekdays: [],
          isFirstOpen: true,
          detailOfficeCleaning: null,
          vatInfo: null,
          isUpdateVatInfoToUser: false,
          hostelPlace: null,
          homeType: null,
          rooms: [],
          options: [],
          roomNumber: '',
          listRoomsImages: [],
        }),
    }),
    {
      name: 'housekeeping-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({ isFirstOpen: state.isFirstOpen }),
    },
  ),
);
